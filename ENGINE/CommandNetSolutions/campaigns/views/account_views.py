"""
Views for account-related functionality in the campaigns app.
"""
import logging
from django.shortcuts import get_object_or_404
from django.views.generic import ListView
# Removed LoginRequiredMixin to disable authentication requirements
# from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.contenttypes.models import ContentType

from campaigns.models import Campaign, DynamicTag
from campaigns.forms.account_forms import AccountFilterForm, DiscoveryFilterForm
from campaigns.services import CampaignAnalysisService
from instagram.models import Accounts, WhiteListEntry, CustomTaggedItem
from taggit.models import Tag

logger = logging.getLogger(__name__)


class CampaignAccountsView(ListView):
    """
    View to display accounts collected by a campaign with filtering and sorting.
    Supports different view types based on URL pattern.
    """
    template_name = 'campaigns/campaign_accounts.html'
    context_object_name = 'accounts'
    paginate_by = 20

    def get_template_names(self):
        """Return different templates based on the view type."""
        view_type = self.get_view_type()
        if view_type == 'list':
            return ['campaigns/campaign_accounts_list.html']
        elif view_type == 'analyzed':
            return ['campaigns/campaign_accounts_analyzed.html']
        return [self.template_name]

    def get_view_type(self):
        """Determine the view type based on the URL pattern."""
        url_name = self.request.resolver_match.url_name
        if url_name == 'campaign_accounts_list':
            return 'list'
        elif url_name == 'campaign_accounts_analyzed':
            return 'analyzed'
        return 'default'

    def get_queryset(self):
        campaign_id = self.kwargs.get('pk')
        self.campaign = get_object_or_404(Campaign, pk=campaign_id)

        # Get all accounts for this campaign
        queryset = Accounts.objects.filter(campaign_id=str(self.campaign.id))

        # Filter based on view type
        view_type = self.get_view_type()
        if view_type == 'analyzed':
            # Only show accounts that have been analyzed (have whitelist entries or tags)
            analyzed_usernames = WhiteListEntry.objects.filter(
                account__campaign_id=str(self.campaign.id)
            ).values_list('account__username', flat=True)
            queryset = queryset.filter(username__in=analyzed_usernames)

        # Use different forms based on view type
        if view_type == 'list':
            # Stage 1: Discovery view - use simplified form
            self.filter_form = DiscoveryFilterForm(self.request.GET or None, campaign=self.campaign)
            if self.filter_form.is_valid():
                queryset = self.filter_form.filter_queryset(queryset, self.campaign)
            else:
                queryset = queryset.order_by('-collection_date')  # Most recent first for list view
        else:
            # Stage 2: Analysis view - use full form
            self.filter_form = AccountFilterForm(self.request.GET or None)
            # Populate the tags choices dynamically
            self.populate_tag_choices(queryset)
            if self.filter_form.is_valid():
                queryset = self.filter_form.filter_queryset(queryset)
            else:
                queryset = queryset.order_by('-followers')

        # Get content type for Accounts model
        content_type = ContentType.objects.get_for_model(Accounts)

        # Get all tags for these accounts
        account_tags = {}
        tagged_items = CustomTaggedItem.objects.filter(
            content_type=content_type,
            object_id__in=[account.username for account in queryset]
        ).select_related('tag')

        # Group tags by account
        for tagged_item in tagged_items:
            if tagged_item.object_id not in account_tags:
                account_tags[tagged_item.object_id] = []
            account_tags[tagged_item.object_id].append(tagged_item.tag.name)

        # Add tags_list attribute to each account
        for account in queryset:
            account.tags_list = account_tags.get(account.username, [])

        return queryset

    def populate_tag_choices(self, queryset):
        """
        Populate the tag choices for the filter form based on the tags used by the accounts.
        """
        # Get content type for Accounts model
        content_type = ContentType.objects.get_for_model(Accounts)

        # Get all tags used by these accounts
        tag_ids = CustomTaggedItem.objects.filter(
            content_type=content_type,
            object_id__in=queryset.values_list('username', flat=True)
        ).values_list('tag_id', flat=True).distinct()

        # Get the tag names
        tags = Tag.objects.filter(id__in=tag_ids).order_by('name')

        # Set the choices for the tags field
        self.filter_form.fields['tags'].choices = [(tag.name, tag.name) for tag in tags]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['campaign'] = self.campaign
        context['filter_form'] = self.filter_form
        context['view_type'] = self.get_view_type()

        # Get analysis stats
        analysis_service = CampaignAnalysisService()
        context['analysis_stats'] = analysis_service.get_campaign_analysis_stats(self.campaign.id)

        # Get account stats
        context['total_accounts'] = self.get_queryset().count()
        context['whitelisted_accounts'] = WhiteListEntry.objects.filter(
            account__campaign_id=str(self.campaign.id)
        ).count()

        # Calculate conversion rate
        if context['total_accounts'] > 0:
            whitelisted = context['whitelisted_accounts']
            total = context['total_accounts']
            context['conversion_rate'] = (whitelisted / total) * 100
        else:
            context['conversion_rate'] = 0

        # Add source information for accounts in list view
        if context['view_type'] == 'list':
            for account in context['accounts']:
                # Determine source type and details based on campaign targets
                # This is a simplified approach since we don't have direct source tracking
                if self.campaign.location_targets.exists():
                    account.source_type = "location"
                    location_target = self.campaign.location_targets.first()
                    account.source_details = f"{location_target.city}, {location_target.country}"
                elif self.campaign.username_targets.exists():
                    account.source_type = "username"
                    username_target = self.campaign.username_targets.first()
                    account.source_details = f"@{username_target.username} ({username_target.get_audience_type_display()})"
                else:
                    account.source_type = None
                    account.source_details = "Unknown source"

        return context


class CampaignWhiteListView(ListView):
    """
    View to display white listed accounts from a campaign.
    """
    template_name = 'campaigns/campaign_whitelist.html'
    context_object_name = 'whitelist_entries'
    paginate_by = 20

    def get_queryset(self):
        campaign_id = self.kwargs.get('pk')
        self.campaign = get_object_or_404(Campaign, pk=campaign_id)
        queryset = WhiteListEntry.objects.filter(
            account__campaign_id=str(self.campaign.id)
        ).select_related('account')

        # Apply sorting if provided in request
        sort_by = self.request.GET.get('sort_by')
        if sort_by:
            # Handle account field sorting
            if sort_by in ['username', '-username']:
                field_name = 'account__username'
                if sort_by.startswith('-'):
                    field_name = f"-{field_name}"
                queryset = queryset.order_by(field_name)
            elif sort_by in ['full_name', '-full_name']:
                field_name = 'account__full_name'
                if sort_by.startswith('-'):
                    field_name = f"-{field_name}"
                queryset = queryset.order_by(field_name)
            elif sort_by in ['followers', '-followers']:
                field_name = 'account__followers'
                if sort_by.startswith('-'):
                    field_name = f"-{field_name}"
                queryset = queryset.order_by(field_name)
            elif sort_by in ['following', '-following']:
                field_name = 'account__following'
                if sort_by.startswith('-'):
                    field_name = f"-{field_name}"
                queryset = queryset.order_by(field_name)
            elif sort_by in ['number_of_posts', '-number_of_posts']:
                field_name = 'account__number_of_posts'
                if sort_by.startswith('-'):
                    field_name = f"-{field_name}"
                queryset = queryset.order_by(field_name)
        else:
            # Default sorting
            queryset = queryset.order_by('account__username')

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['campaign'] = self.campaign

        # Get account stats
        context['total_accounts'] = Accounts.objects.filter(
            campaign_id=str(self.campaign.id)
        ).count()

        context['whitelisted_accounts'] = self.get_queryset().count()

        # Calculate conversion rate
        if context['total_accounts'] > 0:
            whitelisted = context['whitelisted_accounts']
            total = context['total_accounts']
            context['conversion_rate'] = (whitelisted / total) * 100
        else:
            context['conversion_rate'] = 0

        # Get dynamic tags for display
        for entry in context['whitelist_entries']:
            # Log the entry and its tags for debugging
            logger.info(f"Processing whitelist entry for {entry.account.username}")
            logger.info(f"Entry tags: {entry.tags}")

            # Get dynamic tags that match the tags in the whitelist entry
            if hasattr(entry, 'tags') and entry.tags:
                # First try to get matching dynamic tags
                dynamic_tags = DynamicTag.objects.filter(name__in=entry.tags)
                logger.info(f"Found {dynamic_tags.count()} matching dynamic tags")

                # If no dynamic tags found, create simple tag objects from the entry.tags
                if not dynamic_tags.exists():
                    entry.dynamic_tags = [{'name': tag, 'description': '', 'category__name': ''} for tag in entry.tags]
                    logger.info(f"Created {len(entry.dynamic_tags)} simple tag objects")
                else:
                    entry.dynamic_tags = list(dynamic_tags.values('name', 'description', 'category__name'))
                    logger.info(f"Created {len(entry.dynamic_tags)} dynamic tag objects")

                    # Add any tags that weren't found in DynamicTags
                    dynamic_tag_names = [tag['name'] for tag in entry.dynamic_tags]
                    missing_tags = [tag for tag in entry.tags if tag not in dynamic_tag_names]
                    for tag in missing_tags:
                        entry.dynamic_tags.append({'name': tag, 'description': '', 'category__name': ''})

                    if missing_tags:
                        logger.info(f"Added {len(missing_tags)} missing tags: {missing_tags}")
            else:
                logger.info(f"No tags found for entry")
                entry.dynamic_tags = []

        return context
