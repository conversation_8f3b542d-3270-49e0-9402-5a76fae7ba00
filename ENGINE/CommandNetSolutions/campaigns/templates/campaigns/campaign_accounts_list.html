{% extends "campaigns/base.html" %}
{% load campaign_tags %}

{% block title %}All Collected Accounts - {{ campaign.name }}{% endblock %}

{% block campaign_content %}
<div class="d-flex justify-content-between align-items-start mb-4">
    <div>
        <h1 class="page-title">All Collected Accounts</h1>
        <p class="page-subtitle">{{ campaign.name }}</p>
    </div>
    <div>
        <a href="{% url 'campaigns:campaign_detail' campaign.id %}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left me-1"></i> Back to Campaign
        </a>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="campaign-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-users me-2"></i>
                    Collected Accounts ({{ total_accounts }})
                </h5>
            </div>
            <div class="card-body">
                {% if accounts %}
                <!-- Discovery Filter Form -->
                <form method="get" class="mb-4">
                    <div class="row">
                        <div class="col-md-3">
                            {{ filter_form.search }}
                        </div>
                        <div class="col-md-2">
                            {{ filter_form.source_type }}
                        </div>
                        <div class="col-md-2">
                            {{ filter_form.country }}
                        </div>
                        <div class="col-md-2">
                            {{ filter_form.city }}
                        </div>
                        <div class="col-md-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter me-1"></i> Filter
                            </button>
                            <a href="{% url 'campaigns:campaign_accounts_list' campaign.id %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i> Clear
                            </a>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-3">
                            {{ filter_form.audience_type }}
                        </div>
                    </div>
                </form>

                <!-- Accounts Table -->
                <div class="table-responsive">
                    <table class="table table-campaigns">
                        <thead>
                            <tr>
                                <th>Username</th>
                                <th>Source Type</th>
                                <th>Source Details</th>
                                <th>Discovered</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for account in accounts %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        {% if account.is_verified %}
                                        <i class="fas fa-check-circle text-primary me-2" title="Verified Account"></i>
                                        {% endif %}
                                        <strong>@{{ account.username }}</strong>
                                    </div>
                                </td>
                                <td>
                                    {% if account.source_type %}
                                        <span class="badge {% if account.source_type == 'location' %}bg-danger{% else %}bg-info{% endif %}">
                                            {{ account.source_type|title }}
                                        </span>
                                    {% else %}
                                        <span class="badge bg-secondary">Unknown</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <small class="text-muted">{{ account.source_details|default:"No details available" }}</small>
                                </td>
                                <td>
                                    <small class="text-muted">{{ account.collection_date|timesince }} ago</small>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                <nav aria-label="Accounts pagination" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">First</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">Previous</a>
                        </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">Next</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">Last</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

                {% else %}
                <div class="empty-state">
                    <div class="empty-state-icon">
                        <i class="fas fa-users text-muted"></i>
                    </div>
                    <div class="empty-state-text">No accounts have been collected yet.</div>
                    <div class="empty-state-subtext">Launch the campaign to start collecting accounts.</div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
